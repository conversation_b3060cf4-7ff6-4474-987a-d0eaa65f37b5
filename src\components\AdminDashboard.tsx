/**
 * Admin Dashboard for PressureMax
 * Provides admin-only access to template management, user management, and system analytics
 */

import React, { useState, useEffect } from 'react';
import {
  Shield,
  Users,
  FileText,
  BarChart3,
  Settings,
  Plus,
  Edit,
  Trash2,
  Eye,
  Crown,
  DollarSign,
  TrendingUp,
  Activity,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { adminAuth, useAdminAuth } from '../services/adminAuth';
import { AdTemplate, User } from '../types/database';

interface AdminDashboardProps {
  className?: string;
}

export const AdminDashboard: React.FC<AdminDashboardProps> = ({
  className = ''
}) => {
  const { user } = useAuth();
  const { isAdmin, hasPermission, getUserPermissions, logAction } = useAdminAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [adminStats, setAdminStats] = useState<any>(null);
  const [templates, setTemplates] = useState<AdTemplate[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Check admin access
  if (!isAdmin(user)) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <Shield className="mx-auto text-red-500 mb-4" size={64} />
          <h1 className="text-2xl font-bold text-white mb-2">Access Denied</h1>
          <p className="text-gray-400">You don't have permission to access the admin dashboard.</p>
        </div>
      </div>
    );
  }

  useEffect(() => {
    loadAdminData();
    logAction(user?.id || '', 'admin_dashboard_accessed', { timestamp: new Date() });
  }, []);

  const loadAdminData = async () => {
    setIsLoading(true);
    try {
      // Load admin stats
      const stats = await adminAuth.getAdminStats();
      setAdminStats(stats);

      // Load templates and users (mock data for now)
      // In production, these would be API calls
      setTemplates([]);
      setUsers([]);
    } catch (error) {
      console.error('Error loading admin data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const permissions = getUserPermissions(user);

  const adminTabs = [
    {
      id: 'overview',
      name: 'Overview',
      icon: BarChart3,
      permission: 'analytics_view'
    },
    {
      id: 'templates',
      name: 'Template Library',
      icon: FileText,
      permission: 'template_edit'
    },
    {
      id: 'users',
      name: 'User Management',
      icon: Users,
      permission: 'user_manage'
    },
    {
      id: 'settings',
      name: 'System Settings',
      icon: Settings,
      permission: 'system_settings'
    }
  ].filter(tab => hasPermission(user, tab.permission as any));

  const renderOverview = () => (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-white mb-2">Admin Overview</h2>
        <p className="text-gray-400">System-wide statistics and performance metrics</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-gray-800 border border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400">Total Users</p>
              <p className="text-3xl font-bold text-white">{adminStats?.totalUsers || 0}</p>
            </div>
            <Users className="text-cyan-400" size={32} />
          </div>
          <div className="mt-4 flex items-center text-sm">
            <TrendingUp className="text-green-400 mr-1" size={16} />
            <span className="text-green-400">+12% from last month</span>
          </div>
        </div>

        <div className="bg-gray-800 border border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400">Global Templates</p>
              <p className="text-3xl font-bold text-white">{adminStats?.totalTemplates || 0}</p>
            </div>
            <FileText className="text-purple-400" size={32} />
          </div>
          <div className="mt-4 flex items-center text-sm">
            <Activity className="text-blue-400 mr-1" size={16} />
            <span className="text-blue-400">5 active campaigns</span>
          </div>
        </div>

        <div className="bg-gray-800 border border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400">Monthly Revenue</p>
              <p className="text-3xl font-bold text-white">${adminStats?.monthlyRevenue?.toLocaleString() || 0}</p>
            </div>
            <DollarSign className="text-green-400" size={32} />
          </div>
          <div className="mt-4 flex items-center text-sm">
            <TrendingUp className="text-green-400 mr-1" size={16} />
            <span className="text-green-400">+8% from last month</span>
          </div>
        </div>

        <div className="bg-gray-800 border border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400">Total Campaigns</p>
              <p className="text-3xl font-bold text-white">{adminStats?.totalCampaigns || 0}</p>
            </div>
            <BarChart3 className="text-yellow-400" size={32} />
          </div>
          <div className="mt-4 flex items-center text-sm">
            <Activity className="text-yellow-400 mr-1" size={16} />
            <span className="text-yellow-400">234 active</span>
          </div>
        </div>

        <div className="bg-gray-800 border border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400">Total Leads</p>
              <p className="text-3xl font-bold text-white">{adminStats?.totalLeads || 0}</p>
            </div>
            <Users className="text-orange-400" size={32} />
          </div>
          <div className="mt-4 flex items-center text-sm">
            <TrendingUp className="text-green-400 mr-1" size={16} />
            <span className="text-green-400">+15% conversion rate</span>
          </div>
        </div>

        <div className="bg-gray-800 border border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400">Active Subscriptions</p>
              <p className="text-3xl font-bold text-white">{adminStats?.activeSubscriptions || 0}</p>
            </div>
            <Crown className="text-purple-400" size={32} />
          </div>
          <div className="mt-4 flex items-center text-sm">
            <CheckCircle className="text-green-400 mr-1" size={16} />
            <span className="text-green-400">95% retention rate</span>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-gray-800 border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Recent System Activity</h3>
        <div className="space-y-3">
          {[
            { action: 'New template created', user: 'Admin', time: '2 minutes ago', status: 'success' },
            { action: 'User subscription upgraded', user: '<EMAIL>', time: '15 minutes ago', status: 'success' },
            { action: 'Campaign launched', user: '<EMAIL>', time: '1 hour ago', status: 'success' },
            { action: 'Payment failed', user: '<EMAIL>', time: '2 hours ago', status: 'error' },
            { action: 'Template published', user: 'Admin', time: '3 hours ago', status: 'success' }
          ].map((activity, index) => (
            <div key={index} className="flex items-center justify-between py-2 border-b border-gray-700 last:border-b-0">
              <div className="flex items-center space-x-3">
                {activity.status === 'success' ? (
                  <CheckCircle className="text-green-400" size={16} />
                ) : (
                  <XCircle className="text-red-400" size={16} />
                )}
                <div>
                  <p className="text-white text-sm">{activity.action}</p>
                  <p className="text-gray-400 text-xs">{activity.user}</p>
                </div>
              </div>
              <span className="text-gray-400 text-xs">{activity.time}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderTemplateManagement = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Template Library Management</h2>
          <p className="text-gray-400">Create and manage global ad templates for all users</p>
        </div>
        
        {hasPermission(user, 'template_create') && (
          <button className="bg-gradient-to-r from-cyan-500 to-cyan-600 text-black px-4 py-2 font-bold hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-300 flex items-center space-x-2">
            <Plus size={16} />
            <span>Create Global Template</span>
          </button>
        )}
      </div>

      {/* Template Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-gray-800 border border-gray-700 p-4">
          <h3 className="text-sm font-medium text-gray-400">Total Templates</h3>
          <p className="text-2xl font-bold text-white">45</p>
        </div>
        <div className="bg-gray-800 border border-gray-700 p-4">
          <h3 className="text-sm font-medium text-gray-400">Active Templates</h3>
          <p className="text-2xl font-bold text-green-400">38</p>
        </div>
        <div className="bg-gray-800 border border-gray-700 p-4">
          <h3 className="text-sm font-medium text-gray-400">Draft Templates</h3>
          <p className="text-2xl font-bold text-yellow-400">5</p>
        </div>
        <div className="bg-gray-800 border border-gray-700 p-4">
          <h3 className="text-sm font-medium text-gray-400">Archived Templates</h3>
          <p className="text-2xl font-bold text-gray-400">2</p>
        </div>
      </div>

      {/* Templates List */}
      <div className="bg-gray-800 border border-gray-700">
        <div className="p-4 border-b border-gray-700">
          <h3 className="text-lg font-semibold text-white">Global Templates</h3>
        </div>
        
        <div className="p-4">
          <div className="text-center py-12">
            <FileText className="mx-auto text-gray-500 mb-4" size={48} />
            <h3 className="text-lg font-medium text-gray-400 mb-2">Template management coming soon</h3>
            <p className="text-gray-500">Full template CRUD operations will be implemented here</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderUserManagement = () => (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-white">User Management</h2>
        <p className="text-gray-400">Manage user accounts, roles, and permissions</p>
      </div>

      <div className="text-center py-12">
        <Users className="mx-auto text-gray-500 mb-4" size={48} />
        <h3 className="text-lg font-medium text-gray-400 mb-2">User management coming soon</h3>
        <p className="text-gray-500">User CRUD operations and role management will be implemented here</p>
      </div>
    </div>
  );

  const renderSystemSettings = () => (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-white">System Settings</h2>
        <p className="text-gray-400">Configure system-wide settings and preferences</p>
      </div>

      <div className="text-center py-12">
        <Settings className="mx-auto text-gray-500 mb-4" size={48} />
        <h3 className="text-lg font-medium text-gray-400 mb-2">System settings coming soon</h3>
        <p className="text-gray-500">Global configuration options will be implemented here</p>
      </div>
    </div>
  );

  return (
    <div className={`min-h-screen bg-gray-900 ${className}`}>
      {/* Admin Header */}
      <div className="bg-gray-800 border-b border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Shield className="text-cyan-400" size={24} />
            <div>
              <h1 className="text-xl font-bold text-white">Admin Dashboard</h1>
              <p className="text-sm text-gray-400">Welcome back, {user?.name}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <span className="bg-cyan-500 text-black px-2 py-1 text-xs font-bold">ADMIN</span>
            <span className="text-gray-400 text-sm">Last login: {new Date().toLocaleDateString()}</span>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-gray-800 border-b border-gray-700 px-6">
        <nav className="flex space-x-8">
          {adminTabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 py-4 border-b-2 transition-colors ${
                activeTab === tab.id
                  ? 'border-cyan-500 text-cyan-400'
                  : 'border-transparent text-gray-400 hover:text-white'
              }`}
            >
              <tab.icon size={16} />
              <span>{tab.name}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div className="p-6">
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-2 border-cyan-500 border-t-transparent"></div>
          </div>
        ) : (
          <>
            {activeTab === 'overview' && renderOverview()}
            {activeTab === 'templates' && renderTemplateManagement()}
            {activeTab === 'users' && renderUserManagement()}
            {activeTab === 'settings' && renderSystemSettings()}
          </>
        )}
      </div>
    </div>
  );
};
