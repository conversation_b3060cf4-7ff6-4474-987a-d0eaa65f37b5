/**
 * My Campaigns Component for PressureMax
 * Shows user's launched campaigns with performance metrics and management options
 */

import React, { useState, useEffect } from 'react';
import {
  Play,
  Pause,
  Square,
  Edit3,
  BarChart3,
  DollarSign,
  Eye,
  MousePointer,
  Target,
  TrendingUp,
  Calendar,
  ExternalLink,
  AlertCircle,
  CheckCircle,
  Clock,
  Download,
  RefreshCw
} from 'lucide-react';
import { Campaign } from '../types/database';
import { db } from '../services/database';
import { facebookCampaignService } from '../services/facebookCampaignService';
import { facebookIntegration } from '../services/facebookIntegration';
import { CampaignDetailsModal } from './CampaignDetailsModal';

interface MyCampaignsProps {
  className?: string;
  onEditCampaign?: (campaign: Campaign) => void;
  onViewDetails?: (campaign: Campaign) => void;
}

export const MyCampaigns: React.FC<MyCampaignsProps> = ({
  className = '',
  onEditCampaign,
  onViewDetails
}) => {
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isImporting, setIsImporting] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<'all' | 'active' | 'paused' | 'completed'>('all');
  const [importResults, setImportResults] = useState<{ success: number; errors: string[] } | null>(null);
  const [importProgress, setImportProgress] = useState<{ current: number; total: number; currentCampaign: string } | null>(null);
  const [selectedCampaign, setSelectedCampaign] = useState<Campaign | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);

  useEffect(() => {
    loadCampaigns();
  }, []);

  const loadCampaigns = async () => {
    try {
      setIsLoading(true);
      const campaignsData = await db.getCampaigns();
      setCampaigns(campaignsData);
    } catch (error) {
      console.error('Error loading campaigns:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const importFacebookCampaigns = async () => {
    try {
      setIsImporting(true);
      setImportResults(null);

      // Load saved integration status first
      const savedStatus = facebookIntegration.loadSavedIntegration();
      console.log('Loaded Facebook status:', savedStatus);

      if (!savedStatus.isConnected) {
        alert('Please connect your Facebook account first in the Integrations section.');
        return;
      }

      if (!savedStatus.accessToken) {
        alert('Facebook access token not available. Please reconnect your Facebook account.');
        return;
      }

      // Get ad accounts
      console.log('Fetching ad accounts...');
      const adAccounts = await facebookIntegration.getAdAccounts();
      console.log('Ad accounts:', adAccounts);

      if (!adAccounts || adAccounts.length === 0) {
        alert('No Facebook ad accounts found.');
        return;
      }

      let totalImported = 0;
      const errors: string[] = [];

      // Import campaigns from all ad accounts
      for (const account of adAccounts) {
        try {
          console.log(`🔍 Importing campaigns from account: ${account.name}`);

          // Get campaigns from this ad account
          const facebookCampaigns = await facebookCampaignService.getCampaigns(account.account_id);
          console.log(`Found ${facebookCampaigns.length} campaigns in account: ${account.name}`);

          for (let i = 0; i < facebookCampaigns.length; i++) {
            const fbCampaign = facebookCampaigns[i];

            // Update progress
            setImportProgress({
              current: totalImported + i + 1,
              total: facebookCampaigns.length,
              currentCampaign: fbCampaign.name
            });
            try {
              console.log(`📥 Processing campaign: ${fbCampaign.name}`);

              // Get detailed campaign information with retry logic
              let campaignDetails;
              let retryCount = 0;
              const maxRetries = 2;

              while (retryCount <= maxRetries) {
                try {
                  campaignDetails = await facebookCampaignService.getCampaignDetails(fbCampaign.id);
                  break; // Success, exit retry loop
                } catch (detailError) {
                  if (detailError instanceof Error && detailError.message.includes('request limit') && retryCount < maxRetries) {
                    retryCount++;
                    const waitTime = retryCount * 2000; // Exponential backoff: 2s, 4s
                    console.log(`⏳ Rate limit hit, retrying in ${waitTime}ms (attempt ${retryCount}/${maxRetries})`);
                    await new Promise(resolve => setTimeout(resolve, waitTime));
                  } else {
                    throw detailError; // Re-throw if not rate limit or max retries reached
                  }
                }
              }

              // Check if campaign already exists
              const existingCampaigns = await db.getCampaigns();
              const exists = existingCampaigns.some(c => c.facebook_campaign_id === fbCampaign.id);

              if (!exists) {
                // Create campaign record in our database
                const newCampaign: Omit<Campaign, 'id' | 'created_at' | 'updated_at'> = {
                  template_id: 'imported', // Special template ID for imported campaigns
                  name: fbCampaign.name,
                  facebook_campaign_id: fbCampaign.id,
                  budget: fbCampaign.daily_budget ? fbCampaign.daily_budget / 100 : 0, // Convert from cents
                  start_date: new Date(fbCampaign.created_time),
                  end_date: undefined,
                  custom_creative: {
                    headline: `Imported: ${fbCampaign.name}`,
                    description: `Campaign imported from Facebook Ads Manager`,
                    call_to_action: 'Learn More'
                  },
                  custom_targeting: {
                    // Store the actual Facebook targeting data
                    facebook_settings: {
                      objective: fbCampaign.objective,
                      adSets: campaignDetails.adSets.map((adSet: any) => ({
                        id: adSet.id,
                        name: adSet.name,
                        bid_strategy: adSet.bid_strategy,
                        bid_amount: adSet.bid_amount,
                        optimization_goal: adSet.optimization_goal,
                        billing_event: adSet.billing_event,
                        targeting: adSet.targeting,
                        daily_budget: adSet.daily_budget
                      }))
                    }
                  },
                  metrics: {
                    impressions: 0,
                    clicks: 0,
                    ctr: 0,
                    cpc: 0,
                    cpl: 0,
                    leads_generated: 0,
                    spend: 0,
                    last_sync: new Date()
                  },
                  status: fbCampaign.status.toLowerCase() as 'draft' | 'active' | 'paused' | 'completed' | 'error'
                };

                await db.createCampaign(newCampaign);
                totalImported++;
                console.log(`✅ Imported campaign: ${fbCampaign.name}`);
              } else {
                console.log(`⏭️ Campaign already exists: ${fbCampaign.name}`);
              }
            } catch (error) {
              console.error(`❌ Error importing campaign ${fbCampaign.name}:`, error);
              errors.push(`Failed to import "${fbCampaign.name}": ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
          }
        } catch (error) {
          console.error(`❌ Error importing from account ${account.name}:`, error);
          errors.push(`Failed to import from account "${account.name}": ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      setImportResults({ success: totalImported, errors });

      // Reload campaigns to show imported ones
      await loadCampaigns();

    } catch (error) {
      console.error('Error importing Facebook campaigns:', error);
      alert(`Error importing campaigns: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsImporting(false);
      setImportProgress(null);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <Play className="text-green-400" size={16} />;
      case 'paused':
        return <Pause className="text-yellow-400" size={16} />;
      case 'completed':
        return <CheckCircle className="text-blue-400" size={16} />;
      default:
        return <Clock className="text-gray-400" size={16} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-400 bg-green-500/20';
      case 'paused':
        return 'text-yellow-400 bg-yellow-500/20';
      case 'completed':
        return 'text-blue-400 bg-blue-500/20';
      default:
        return 'text-gray-400 bg-gray-500/20';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  const filteredCampaigns = campaigns.filter(campaign => 
    selectedStatus === 'all' || campaign.status === selectedStatus
  );

  const totalSpent = campaigns.reduce((sum, campaign) => sum + (campaign.metrics?.spend || 0), 0);
  const totalConversions = campaigns.reduce((sum, campaign) => sum + (campaign.metrics?.leads_generated || 0), 0);
  const avgROAS = campaigns.length > 0
    ? campaigns.reduce((sum, campaign) => {
        const spend = campaign.metrics?.spend || 0;
        const leads = campaign.metrics?.leads_generated || 0;
        const cpl = campaign.metrics?.cpl || 0;
        // Calculate ROAS as (leads * average lead value) / spend
        // Assuming average lead value of $100 for pressure washing
        const leadValue = 100;
        return spend > 0 ? sum + ((leads * leadValue) / spend) : sum;
      }, 0) / campaigns.length
    : 0;

  if (isLoading) {
    return (
      <div className={`bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-20 bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="bg-cyan-500 p-2 rounded-lg">
            <BarChart3 className="text-black" size={20} />
          </div>
          <div>
            <h3 className="text-xl font-bold text-white">My Campaigns</h3>
            <p className="text-sm text-gray-400">Track and manage your active campaigns</p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={importFacebookCampaigns}
            disabled={isImporting}
            className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
            title="Import campaigns with detailed settings from Facebook Ads Manager"
          >
            {isImporting ? (
              <RefreshCw className="animate-spin" size={16} />
            ) : (
              <Download size={16} />
            )}
            <span>{isImporting ? 'Importing...' : 'Import from Facebook'}</span>
          </button>
        </div>

        {/* Status Filter */}
        <div className="flex space-x-1 bg-gray-800/50 p-1 rounded-lg">
          {(['all', 'active', 'paused', 'completed'] as const).map((status) => (
            <button
              key={status}
              onClick={() => setSelectedStatus(status)}
              className={`px-3 py-1 text-xs font-medium rounded transition-colors ${
                selectedStatus === status
                  ? 'bg-cyan-500 text-black'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Import Progress */}
      {importProgress && (
        <div className="mb-6 p-4 bg-blue-900/30 rounded-xl border border-blue-600">
          <div className="flex items-center space-x-3">
            <RefreshCw className="text-blue-400 animate-spin" size={20} />
            <div className="flex-1">
              <h4 className="text-white font-semibold mb-2">Importing Facebook Campaigns</h4>
              <p className="text-gray-300 text-sm mb-3">
                Processing campaign {importProgress.current} of {importProgress.total}
              </p>
              <div className="bg-gray-700 rounded-full h-2 mb-2">
                <div
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(importProgress.current / importProgress.total) * 100}%` }}
                />
              </div>
              <p className="text-gray-400 text-xs truncate">
                Current: {importProgress.currentCampaign}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Import Results */}
      {importResults && (
        <div className="mb-6 p-4 bg-gray-800/30 rounded-xl border border-gray-600">
          <div className="flex items-start space-x-3">
            <CheckCircle className="text-green-400 mt-1" size={20} />
            <div className="flex-1">
              <h4 className="text-white font-semibold mb-2">Import Complete</h4>
              <p className="text-gray-300 text-sm mb-2">
                Successfully imported {importResults.success} campaign{importResults.success !== 1 ? 's' : ''} from Facebook.
              </p>
              {importResults.errors.length > 0 && (
                <div className="mt-3">
                  <p className="text-yellow-400 text-sm font-medium mb-2">
                    {importResults.errors.length} error{importResults.errors.length !== 1 ? 's' : ''} occurred:
                  </p>
                  <ul className="text-gray-400 text-xs space-y-1">
                    {importResults.errors.map((error, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <span className="text-yellow-400">•</span>
                        <span>{error}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              <button
                onClick={() => setImportResults(null)}
                className="mt-3 text-cyan-400 hover:text-cyan-300 text-sm"
              >
                Dismiss
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-gray-800/30 rounded-xl p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Spent</p>
              <p className="text-xl font-bold text-white">{formatCurrency(totalSpent)}</p>
            </div>
            <DollarSign className="text-green-400" size={24} />
          </div>
        </div>
        
        <div className="bg-gray-800/30 rounded-xl p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Conversions</p>
              <p className="text-xl font-bold text-white">{formatNumber(totalConversions)}</p>
            </div>
            <Target className="text-blue-400" size={24} />
          </div>
        </div>
        
        <div className="bg-gray-800/30 rounded-xl p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Avg ROAS</p>
              <p className="text-xl font-bold text-white">{avgROAS.toFixed(1)}x</p>
            </div>
            <TrendingUp className="text-cyan-400" size={24} />
          </div>
        </div>
      </div>

      {/* Campaigns List */}
      {filteredCampaigns.length === 0 ? (
        <div className="text-center py-12">
          <div className="bg-gray-800/30 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <BarChart3 className="text-gray-400" size={32} />
          </div>
          <h4 className="text-white font-semibold mb-2">
            {selectedStatus === 'all' ? 'No Campaigns Yet' : `No ${selectedStatus} Campaigns`}
          </h4>
          <p className="text-gray-400 text-sm mb-4">
            {selectedStatus === 'all' 
              ? 'Launch your first campaign from the Ad Templates section'
              : `You don't have any ${selectedStatus} campaigns at the moment`
            }
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredCampaigns.map((campaign) => (
            <div
              key={campaign.id}
              className="bg-gray-800/30 rounded-xl p-4 hover:bg-gray-800/50 transition-colors"
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h4 className="text-white font-semibold">{campaign.name}</h4>
                    <div className={`flex items-center space-x-1 px-2 py-1 text-xs font-medium rounded ${getStatusColor(campaign.status)}`}>
                      {getStatusIcon(campaign.status)}
                      <span>{campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}</span>
                    </div>
                  </div>
                  <p className="text-gray-400 text-sm">{campaign.custom_creative?.headline || 'Campaign created from template'}</p>

                  {/* Show Facebook settings for imported campaigns */}
                  {campaign.template_id === 'imported' && campaign.custom_targeting?.facebook_settings && (
                    <div className="mt-3 p-3 bg-gray-700/30 rounded-lg border border-gray-600">
                      <h5 className="text-white text-sm font-semibold mb-2 flex items-center space-x-2">
                        <ExternalLink size={14} />
                        <span>Facebook Campaign Settings</span>
                      </h5>
                      <div className="space-y-2 text-xs">
                        <div className="flex justify-between">
                          <span className="text-gray-400">Objective:</span>
                          <span className="text-white">{campaign.custom_targeting.facebook_settings.objective}</span>
                        </div>
                        {campaign.custom_targeting.facebook_settings.adSets?.map((adSet: any, index: number) => (
                          <div key={adSet.id} className="border-t border-gray-600 pt-2 mt-2">
                            <div className="text-gray-300 font-medium mb-1">Ad Set: {adSet.name}</div>
                            <div className="grid grid-cols-2 gap-2 text-xs">
                              <div className="flex justify-between">
                                <span className="text-gray-400">Bid Strategy:</span>
                                <span className="text-cyan-400 font-medium">{adSet.bid_strategy}</span>
                              </div>
                              {adSet.bid_amount && (
                                <div className="flex justify-between">
                                  <span className="text-gray-400">Bid Amount:</span>
                                  <span className="text-white">${(adSet.bid_amount / 100).toFixed(2)}</span>
                                </div>
                              )}
                              <div className="flex justify-between">
                                <span className="text-gray-400">Optimization:</span>
                                <span className="text-white">{adSet.optimization_goal}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-400">Billing:</span>
                                <span className="text-white">{adSet.billing_event}</span>
                              </div>
                              {adSet.daily_budget && (
                                <div className="flex justify-between col-span-2">
                                  <span className="text-gray-400">Daily Budget:</span>
                                  <span className="text-white">${(adSet.daily_budget / 100).toFixed(2)}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => {
                      setSelectedCampaign(campaign);
                      setIsDetailsModalOpen(true);
                    }}
                    className="p-2 text-gray-400 hover:text-cyan-400 hover:bg-gray-700/50 rounded-lg transition-colors"
                    title="View Details"
                  >
                    <Eye size={16} />
                  </button>
                  <button
                    onClick={() => onEditCampaign?.(campaign)}
                    className="p-2 text-gray-400 hover:text-blue-400 hover:bg-gray-700/50 rounded-lg transition-colors"
                    title="Edit Campaign"
                  >
                    <Edit3 size={16} />
                  </button>
                  <a
                    href={`https://business.facebook.com/adsmanager/manage/campaigns?act=${campaign.platform_campaign_id}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="p-2 text-gray-400 hover:text-green-400 hover:bg-gray-700/50 rounded-lg transition-colors"
                    title="View on Facebook"
                  >
                    <ExternalLink size={16} />
                  </a>
                </div>
              </div>

              {/* Performance Metrics */}
              {campaign.metrics && (
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                  <div>
                    <p className="text-gray-400">Spent</p>
                    <p className="text-white font-medium">{formatCurrency(campaign.metrics.spend)}</p>
                  </div>
                  <div>
                    <p className="text-gray-400">Impressions</p>
                    <p className="text-white font-medium">{formatNumber(campaign.metrics.impressions)}</p>
                  </div>
                  <div>
                    <p className="text-gray-400">Clicks</p>
                    <p className="text-white font-medium">{formatNumber(campaign.metrics.clicks)}</p>
                  </div>
                  <div>
                    <p className="text-gray-400">Leads</p>
                    <p className="text-white font-medium">{formatNumber(campaign.metrics.leads_generated)}</p>
                  </div>
                  <div>
                    <p className="text-gray-400">CPL</p>
                    <p className="text-white font-medium">{formatCurrency(campaign.metrics.cpl)}</p>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Campaign Details Modal */}
      <CampaignDetailsModal
        campaign={selectedCampaign}
        isOpen={isDetailsModalOpen}
        onClose={() => {
          setIsDetailsModalOpen(false);
          setSelectedCampaign(null);
        }}
      />
    </div>
  );
};
