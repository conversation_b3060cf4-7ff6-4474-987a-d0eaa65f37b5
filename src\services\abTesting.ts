// A/B Testing Service for Ad Template Optimization
import { AdTemplate, Campaign, Lead } from '../types/database';
import { db } from './database';

export interface ABTest {
  id: string;
  name: string;
  description: string;
  status: 'draft' | 'running' | 'completed' | 'paused';
  
  // Test Configuration
  testType: 'headline' | 'primary_text' | 'cta' | 'targeting' | 'creative' | 'full_template';
  trafficSplit: number; // Percentage for variant A (remainder goes to B)
  
  // Templates being tested
  variantA: {
    templateId: string;
    name: string;
    campaignId?: string;
  };
  variantB: {
    templateId: string;
    name: string;
    campaignId?: string;
  };
  
  // Test Parameters
  minSampleSize: number;
  confidenceLevel: number; // 90, 95, or 99
  testDuration: number; // days
  primaryMetric: 'ctr' | 'cpl' | 'conversion_rate' | 'roi';
  
  // Results
  results?: ABTestResults;
  
  // Timestamps
  startDate?: Date;
  endDate?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface ABTestResults {
  variantA: ABTestMetrics;
  variantB: ABTestMetrics;
  winner?: 'A' | 'B' | 'inconclusive';
  confidence: number;
  significance: number;
  recommendation: string;
  improvementPercent?: number;
}

export interface ABTestMetrics {
  impressions: number;
  clicks: number;
  ctr: number;
  spend: number;
  leads: number;
  cpl: number;
  conversions: number;
  conversionRate: number;
  revenue: number;
  roi: number;
}

export interface ABTestTemplate {
  baseTemplateId: string;
  variations: {
    headline?: string[];
    primaryText?: string[];
    callToAction?: string[];
    description?: string[];
  };
  testName: string;
}

export class ABTestingService {
  private tests: ABTest[] = [];

  // Create a new A/B test
  async createABTest(config: {
    name: string;
    description: string;
    baseTemplateId: string;
    testType: ABTest['testType'];
    variations: any;
    trafficSplit?: number;
    minSampleSize?: number;
    confidenceLevel?: number;
    testDuration?: number;
    primaryMetric?: ABTest['primaryMetric'];
  }): Promise<ABTest> {
    const baseTemplate = await db.getAdTemplateById(config.baseTemplateId);
    if (!baseTemplate) {
      throw new Error('Base template not found');
    }

    // Create variant templates
    const variantA = await this.createVariantTemplate(baseTemplate, config.variations.A, 'A');
    const variantB = await this.createVariantTemplate(baseTemplate, config.variations.B, 'B');

    const test: ABTest = {
      id: `test-${Date.now()}`,
      name: config.name,
      description: config.description,
      status: 'draft',
      testType: config.testType,
      trafficSplit: config.trafficSplit || 50,
      variantA: {
        templateId: variantA.id,
        name: `${config.name} - Variant A`
      },
      variantB: {
        templateId: variantB.id,
        name: `${config.name} - Variant B`
      },
      minSampleSize: config.minSampleSize || 100,
      confidenceLevel: config.confidenceLevel || 95,
      testDuration: config.testDuration || 14,
      primaryMetric: config.primaryMetric || 'cpl',
      created_at: new Date(),
      updated_at: new Date()
    };

    this.tests.push(test);
    return test;
  }

  // Create a variant template based on the base template
  private async createVariantTemplate(
    baseTemplate: AdTemplate, 
    variations: any, 
    variant: 'A' | 'B'
  ): Promise<AdTemplate> {
    const variantTemplate = {
      ...baseTemplate,
      id: undefined, // Will be generated
      name: `${baseTemplate.name} - Variant ${variant}`,
      creative: {
        ...baseTemplate.creative,
        ...(variations.headline && { headline: variations.headline }),
        ...(variations.primaryText && { primary_text: variations.primaryText }),
        ...(variations.callToAction && { call_to_action: variations.callToAction }),
        ...(variations.description && { description: variations.description })
      },
      status: 'draft' as const,
      is_featured: false
    };

    return await db.createAdTemplate(variantTemplate);
  }

  // Start an A/B test
  async startTest(testId: string): Promise<void> {
    const test = this.tests.find(t => t.id === testId);
    if (!test) {
      throw new Error('Test not found');
    }

    if (test.status !== 'draft') {
      throw new Error('Test can only be started from draft status');
    }

    // Deploy campaigns for both variants
    // In a real implementation, this would create actual Facebook campaigns
    test.variantA.campaignId = `campaign-a-${Date.now()}`;
    test.variantB.campaignId = `campaign-b-${Date.now()}`;

    test.status = 'running';
    test.startDate = new Date();
    test.endDate = new Date(Date.now() + test.testDuration * 24 * 60 * 60 * 1000);
    test.updated_at = new Date();

    console.log(`A/B test "${test.name}" started with ${test.trafficSplit}/${100 - test.trafficSplit} traffic split`);
  }

  // Stop an A/B test
  async stopTest(testId: string): Promise<void> {
    const test = this.tests.find(t => t.id === testId);
    if (!test) {
      throw new Error('Test not found');
    }

    test.status = 'completed';
    test.endDate = new Date();
    test.updated_at = new Date();

    // Calculate final results
    await this.calculateTestResults(testId);

    console.log(`A/B test "${test.name}" stopped`);
  }

  // Calculate test results and determine winner
  async calculateTestResults(testId: string): Promise<ABTestResults> {
    const test = this.tests.find(t => t.id === testId);
    if (!test) {
      throw new Error('Test not found');
    }

    // Get campaign data for both variants
    const variantAMetrics = await this.getVariantMetrics(test.variantA.campaignId!);
    const variantBMetrics = await this.getVariantMetrics(test.variantB.campaignId!);

    // Perform statistical significance test
    const significance = this.calculateStatisticalSignificance(
      variantAMetrics,
      variantBMetrics,
      test.primaryMetric
    );

    // Determine winner
    let winner: 'A' | 'B' | 'inconclusive' = 'inconclusive';
    let improvementPercent = 0;

    if (significance >= (test.confidenceLevel / 100)) {
      const aValue = this.getMetricValue(variantAMetrics, test.primaryMetric);
      const bValue = this.getMetricValue(variantBMetrics, test.primaryMetric);

      if (test.primaryMetric === 'cpl') {
        // Lower is better for CPL
        winner = aValue < bValue ? 'A' : 'B';
        improvementPercent = ((Math.max(aValue, bValue) - Math.min(aValue, bValue)) / Math.max(aValue, bValue)) * 100;
      } else {
        // Higher is better for other metrics
        winner = aValue > bValue ? 'A' : 'B';
        improvementPercent = ((Math.max(aValue, bValue) - Math.min(aValue, bValue)) / Math.min(aValue, bValue)) * 100;
      }
    }

    const results: ABTestResults = {
      variantA: variantAMetrics,
      variantB: variantBMetrics,
      winner,
      confidence: test.confidenceLevel,
      significance: significance * 100,
      improvementPercent,
      recommendation: this.generateRecommendation(winner, improvementPercent, test.primaryMetric)
    };

    test.results = results;
    test.updated_at = new Date();

    return results;
  }

  // Get metrics for a variant (mock implementation)
  private async getVariantMetrics(campaignId: string): Promise<ABTestMetrics> {
    // In a real implementation, this would fetch actual campaign data
    // For now, we'll generate mock data
    const impressions = Math.floor(Math.random() * 10000) + 5000;
    const clicks = Math.floor(impressions * (Math.random() * 0.05 + 0.01)); // 1-6% CTR
    const spend = Math.floor(Math.random() * 1000) + 500;
    const leads = Math.floor(clicks * (Math.random() * 0.3 + 0.1)); // 10-40% conversion
    const conversions = Math.floor(leads * (Math.random() * 0.4 + 0.1)); // 10-50% lead to sale
    const revenue = conversions * (Math.random() * 400 + 200); // $200-600 per conversion

    return {
      impressions,
      clicks,
      ctr: (clicks / impressions) * 100,
      spend,
      leads,
      cpl: leads > 0 ? spend / leads : 0,
      conversions,
      conversionRate: leads > 0 ? (conversions / leads) * 100 : 0,
      revenue,
      roi: spend > 0 ? ((revenue - spend) / spend) * 100 : 0
    };
  }

  // Calculate statistical significance (simplified)
  private calculateStatisticalSignificance(
    variantA: ABTestMetrics,
    variantB: ABTestMetrics,
    metric: ABTest['primaryMetric']
  ): number {
    const aValue = this.getMetricValue(variantA, metric);
    const bValue = this.getMetricValue(variantB, metric);
    const aSample = variantA.impressions;
    const bSample = variantB.impressions;

    // Simplified significance calculation
    // In a real implementation, you'd use proper statistical tests
    const pooledStdError = Math.sqrt((aValue * (1 - aValue) / aSample) + (bValue * (1 - bValue) / bSample));
    const zScore = Math.abs(aValue - bValue) / pooledStdError;
    
    // Convert z-score to confidence level (simplified)
    if (zScore > 2.58) return 0.99; // 99% confidence
    if (zScore > 1.96) return 0.95; // 95% confidence
    if (zScore > 1.65) return 0.90; // 90% confidence
    return zScore / 2.58 * 0.90; // Approximate confidence
  }

  // Get metric value for comparison
  private getMetricValue(metrics: ABTestMetrics, metric: ABTest['primaryMetric']): number {
    switch (metric) {
      case 'ctr': return metrics.ctr / 100;
      case 'cpl': return metrics.cpl;
      case 'conversion_rate': return metrics.conversionRate / 100;
      case 'roi': return metrics.roi / 100;
      default: return 0;
    }
  }

  // Generate recommendation based on results
  private generateRecommendation(
    winner: 'A' | 'B' | 'inconclusive',
    improvementPercent: number,
    metric: ABTest['primaryMetric']
  ): string {
    if (winner === 'inconclusive') {
      return 'Test results are inconclusive. Consider running the test longer or with a larger sample size.';
    }

    const metricName = {
      'ctr': 'click-through rate',
      'cpl': 'cost per lead',
      'conversion_rate': 'conversion rate',
      'roi': 'return on investment'
    }[metric];

    return `Variant ${winner} is the winner with a ${improvementPercent.toFixed(1)}% improvement in ${metricName}. Consider implementing this variant for all future campaigns.`;
  }

  // Get all tests
  getTests(): ABTest[] {
    return this.tests;
  }

  // Get test by ID
  getTest(testId: string): ABTest | null {
    return this.tests.find(t => t.id === testId) || null;
  }

  // Get running tests
  getRunningTests(): ABTest[] {
    return this.tests.filter(t => t.status === 'running');
  }

  // Get completed tests
  getCompletedTests(): ABTest[] {
    return this.tests.filter(t => t.status === 'completed');
  }

  // Generate quick test suggestions
  generateTestSuggestions(templateId: string): ABTestTemplate[] {
    return [
      {
        baseTemplateId: templateId,
        testName: 'Headline Optimization',
        variations: {
          headline: [
            'Transform Your Home\'s Curb Appeal Today',
            'Professional Pressure Washing - Instant Results',
            'Get Your Home Summer-Ready with Expert Cleaning'
          ]
        }
      },
      {
        baseTemplateId: templateId,
        testName: 'Call-to-Action Test',
        variations: {
          callToAction: [
            'Get Free Estimate',
            'Book Now',
            'Schedule Today',
            'Request Quote'
          ]
        }
      },
      {
        baseTemplateId: templateId,
        testName: 'Primary Text Variation',
        variations: {
          primaryText: [
            'Professional pressure washing that removes years of dirt and grime. See the dramatic difference!',
            'Transform your property with our expert pressure washing services. Fast, reliable, and affordable.',
            'Don\'t let dirt and stains damage your property. Our professional cleaning restores like-new appearance.'
          ]
        }
      }
    ];
  }

  // Get test statistics
  getTestStatistics(): {
    totalTests: number;
    runningTests: number;
    completedTests: number;
    avgImprovementPercent: number;
    successfulTests: number;
  } {
    const completedTests = this.getCompletedTests();
    const successfulTests = completedTests.filter(t => t.results?.winner !== 'inconclusive');
    const avgImprovement = successfulTests.length > 0
      ? successfulTests.reduce((sum, t) => sum + (t.results?.improvementPercent || 0), 0) / successfulTests.length
      : 0;

    return {
      totalTests: this.tests.length,
      runningTests: this.getRunningTests().length,
      completedTests: completedTests.length,
      avgImprovementPercent: avgImprovement,
      successfulTests: successfulTests.length
    };
  }
}

// Export singleton instance
export const abTestingService = new ABTestingService();
