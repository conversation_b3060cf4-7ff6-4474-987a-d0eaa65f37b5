// Analytics Service for Campaign Performance Tracking
import { AdTemplate, Campaign, Lead } from '../types/database';
import { db } from './database';

export interface CampaignMetrics {
  campaignId: string;
  campaignName: string;
  templateId: string;
  templateName: string;
  
  // Spend & Budget
  totalSpend: number;
  dailyBudget: number;
  remainingBudget: number;
  
  // Impressions & Reach
  impressions: number;
  reach: number;
  frequency: number;
  
  // Clicks & Engagement
  clicks: number;
  ctr: number; // Click-through rate
  cpc: number; // Cost per click
  
  // Leads & Conversions
  leads: number;
  cpl: number; // Cost per lead
  conversionRate: number;
  appointments: number;
  appointmentRate: number;
  
  // Quality Metrics
  hotLeads: number;
  warmLeads: number;
  coldLeads: number;
  avgLeadScore: number;
  
  // ROI Metrics
  estimatedRevenue: number;
  roi: number;
  roas: number; // Return on ad spend
  
  // Time-based
  dateRange: {
    start: Date;
    end: Date;
  };
  lastUpdated: Date;
}

export interface AnalyticsSummary {
  totalCampaigns: number;
  activeCampaigns: number;
  totalSpend: number;
  totalLeads: number;
  avgCPL: number;
  avgConversionRate: number;
  totalAppointments: number;
  estimatedRevenue: number;
  overallROI: number;
}

export interface LeadQualityMetrics {
  totalLeads: number;
  hotLeads: number;
  warmLeads: number;
  coldLeads: number;
  avgScore: number;
  conversionByQuality: {
    hot: { leads: number; conversions: number; rate: number };
    warm: { leads: number; conversions: number; rate: number };
    cold: { leads: number; conversions: number; rate: number };
  };
}

export interface PerformanceByService {
  serviceType: string;
  campaigns: number;
  spend: number;
  leads: number;
  cpl: number;
  conversionRate: number;
  appointments: number;
  revenue: number;
  roi: number;
}

export interface TimeSeriesData {
  date: string;
  impressions: number;
  clicks: number;
  spend: number;
  leads: number;
  appointments: number;
  ctr: number;
  cpl: number;
}

export class AnalyticsService {
  // Get comprehensive campaign metrics
  async getCampaignMetrics(campaignId: string): Promise<CampaignMetrics | null> {
    const campaign = await db.getCampaignById(campaignId);
    if (!campaign) return null;

    const template = await db.getAdTemplateById(campaign.template_id);
    if (!template) return null;

    const leads = await db.getLeadsByCampaign(campaignId);
    
    // Calculate metrics
    const totalLeads = leads.length;
    const appointments = leads.filter(lead => lead.appointment_scheduled).length;
    const hotLeads = leads.filter(lead => lead.quality === 'hot').length;
    const warmLeads = leads.filter(lead => lead.quality === 'warm').length;
    const coldLeads = leads.filter(lead => lead.quality === 'cold').length;
    
    const avgLeadScore = totalLeads > 0 
      ? leads.reduce((sum, lead) => sum + lead.score, 0) / totalLeads 
      : 0;

    const estimatedRevenue = this.calculateEstimatedRevenue(leads);
    const roi = campaign.metrics.spend > 0 ? (estimatedRevenue - campaign.metrics.spend) / campaign.metrics.spend * 100 : 0;
    const roas = campaign.metrics.spend > 0 ? estimatedRevenue / campaign.metrics.spend : 0;

    return {
      campaignId: campaign.id,
      campaignName: campaign.name,
      templateId: template.id,
      templateName: template.name,
      
      totalSpend: campaign.metrics.spend,
      dailyBudget: campaign.budget,
      remainingBudget: Math.max(0, campaign.budget * 30 - campaign.metrics.spend), // Assuming monthly budget
      
      impressions: campaign.metrics.impressions,
      reach: Math.floor(campaign.metrics.impressions * 0.7), // Estimated reach
      frequency: campaign.metrics.impressions > 0 ? campaign.metrics.impressions / Math.floor(campaign.metrics.impressions * 0.7) : 0,
      
      clicks: campaign.metrics.clicks,
      ctr: campaign.metrics.ctr,
      cpc: campaign.metrics.cpc,
      
      leads: totalLeads,
      cpl: campaign.metrics.cpl,
      conversionRate: campaign.metrics.clicks > 0 ? (totalLeads / campaign.metrics.clicks) * 100 : 0,
      appointments,
      appointmentRate: totalLeads > 0 ? (appointments / totalLeads) * 100 : 0,
      
      hotLeads,
      warmLeads,
      coldLeads,
      avgLeadScore,
      
      estimatedRevenue,
      roi,
      roas,
      
      dateRange: {
        start: campaign.start_date,
        end: campaign.end_date || new Date()
      },
      lastUpdated: campaign.metrics.last_sync
    };
  }

  // Get overall analytics summary
  async getAnalyticsSummary(): Promise<AnalyticsSummary> {
    const campaigns = await db.getCampaigns();
    const allLeads = await db.getLeads();
    
    const activeCampaigns = campaigns.filter(c => c.status === 'active').length;
    const totalSpend = campaigns.reduce((sum, c) => sum + c.metrics.spend, 0);
    const totalLeads = allLeads.length;
    const totalAppointments = allLeads.filter(l => l.appointment_scheduled).length;
    
    const avgCPL = totalLeads > 0 ? totalSpend / totalLeads : 0;
    const avgConversionRate = campaigns.length > 0 
      ? campaigns.reduce((sum, c) => sum + (c.metrics.clicks > 0 ? (c.metrics.leads_generated / c.metrics.clicks) * 100 : 0), 0) / campaigns.length
      : 0;

    const estimatedRevenue = this.calculateEstimatedRevenue(allLeads);
    const overallROI = totalSpend > 0 ? (estimatedRevenue - totalSpend) / totalSpend * 100 : 0;

    return {
      totalCampaigns: campaigns.length,
      activeCampaigns,
      totalSpend,
      totalLeads,
      avgCPL,
      avgConversionRate,
      totalAppointments,
      estimatedRevenue,
      overallROI
    };
  }

  // Get lead quality metrics
  async getLeadQualityMetrics(): Promise<LeadQualityMetrics> {
    const leads = await db.getLeads();
    
    const hotLeads = leads.filter(l => l.quality === 'hot');
    const warmLeads = leads.filter(l => l.quality === 'warm');
    const coldLeads = leads.filter(l => l.quality === 'cold');
    
    const avgScore = leads.length > 0 
      ? leads.reduce((sum, lead) => sum + lead.score, 0) / leads.length 
      : 0;

    const getConversionRate = (qualityLeads: Lead[]) => {
      const conversions = qualityLeads.filter(l => l.status === 'converted').length;
      return qualityLeads.length > 0 ? (conversions / qualityLeads.length) * 100 : 0;
    };

    return {
      totalLeads: leads.length,
      hotLeads: hotLeads.length,
      warmLeads: warmLeads.length,
      coldLeads: coldLeads.length,
      avgScore,
      conversionByQuality: {
        hot: {
          leads: hotLeads.length,
          conversions: hotLeads.filter(l => l.status === 'converted').length,
          rate: getConversionRate(hotLeads)
        },
        warm: {
          leads: warmLeads.length,
          conversions: warmLeads.filter(l => l.status === 'converted').length,
          rate: getConversionRate(warmLeads)
        },
        cold: {
          leads: coldLeads.length,
          conversions: coldLeads.filter(l => l.status === 'converted').length,
          rate: getConversionRate(coldLeads)
        }
      }
    };
  }

  // Get performance by service type
  async getPerformanceByService(): Promise<PerformanceByService[]> {
    const templates = await db.getAdTemplates();
    const campaigns = await db.getCampaigns();
    const leads = await db.getLeads();

    const serviceTypes = [...new Set(templates.map(t => t.service))];
    
    return serviceTypes.map(serviceType => {
      const serviceTemplates = templates.filter(t => t.service === serviceType);
      const serviceCampaigns = campaigns.filter(c => 
        serviceTemplates.some(t => t.id === c.template_id)
      );
      const serviceLeads = leads.filter(l => 
        serviceCampaigns.some(c => c.id === l.campaign_id)
      );

      const totalSpend = serviceCampaigns.reduce((sum, c) => sum + c.metrics.spend, 0);
      const totalLeads = serviceLeads.length;
      const appointments = serviceLeads.filter(l => l.appointment_scheduled).length;
      const conversions = serviceLeads.filter(l => l.status === 'converted').length;
      const revenue = this.calculateEstimatedRevenue(serviceLeads);

      return {
        serviceType,
        campaigns: serviceCampaigns.length,
        spend: totalSpend,
        leads: totalLeads,
        cpl: totalLeads > 0 ? totalSpend / totalLeads : 0,
        conversionRate: totalLeads > 0 ? (conversions / totalLeads) * 100 : 0,
        appointments,
        revenue,
        roi: totalSpend > 0 ? (revenue - totalSpend) / totalSpend * 100 : 0
      };
    });
  }

  // Get time series data for charts
  async getTimeSeriesData(days: number = 30): Promise<TimeSeriesData[]> {
    const campaigns = await db.getCampaigns();
    const leads = await db.getLeads();
    
    const data: TimeSeriesData[] = [];
    const today = new Date();
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      
      // Filter data for this date (mock implementation)
      const dayLeads = leads.filter(l => 
        l.created_at.toISOString().split('T')[0] === dateStr
      );
      
      const dayAppointments = dayLeads.filter(l => l.appointment_scheduled).length;
      const daySpend = campaigns.reduce((sum, c) => sum + (c.metrics.spend / 30), 0); // Daily average
      const dayImpressions = campaigns.reduce((sum, c) => sum + (c.metrics.impressions / 30), 0);
      const dayClicks = campaigns.reduce((sum, c) => sum + (c.metrics.clicks / 30), 0);
      
      data.push({
        date: dateStr,
        impressions: Math.floor(dayImpressions),
        clicks: Math.floor(dayClicks),
        spend: daySpend,
        leads: dayLeads.length,
        appointments: dayAppointments,
        ctr: dayImpressions > 0 ? (dayClicks / dayImpressions) * 100 : 0,
        cpl: dayLeads.length > 0 ? daySpend / dayLeads.length : 0
      });
    }
    
    return data;
  }

  // Calculate estimated revenue based on leads
  private calculateEstimatedRevenue(leads: Lead[]): number {
    const serviceValues = {
      'House Washing': 400,
      'Driveway Cleaning': 200,
      'Deck Restoration': 600,
      'Roof Cleaning': 800,
      'Commercial Building Washing': 1200,
      'Parking Lot Cleaning': 500
    };

    return leads.reduce((total, lead) => {
      if (lead.status === 'converted') {
        const serviceValue = serviceValues[lead.service_interest as keyof typeof serviceValues] || 300;
        return total + serviceValue;
      }
      
      // Estimate potential revenue based on lead quality and status
      if (lead.appointment_scheduled) {
        const serviceValue = serviceValues[lead.service_interest as keyof typeof serviceValues] || 300;
        const conversionProbability = lead.quality === 'hot' ? 0.7 : lead.quality === 'warm' ? 0.4 : 0.2;
        return total + (serviceValue * conversionProbability);
      }
      
      return total;
    }, 0);
  }

  // Get top performing templates
  async getTopPerformingTemplates(limit: number = 5): Promise<Array<{
    template: AdTemplate;
    metrics: {
      campaigns: number;
      leads: number;
      cpl: number;
      conversionRate: number;
      roi: number;
    };
  }>> {
    const templates = await db.getAdTemplates();
    const campaigns = await db.getCampaigns();
    const leads = await db.getLeads();

    const templatePerformance = templates.map(template => {
      const templateCampaigns = campaigns.filter(c => c.template_id === template.id);
      const templateLeads = leads.filter(l => 
        templateCampaigns.some(c => c.id === l.campaign_id)
      );

      const totalSpend = templateCampaigns.reduce((sum, c) => sum + c.metrics.spend, 0);
      const conversions = templateLeads.filter(l => l.status === 'converted').length;
      const revenue = this.calculateEstimatedRevenue(templateLeads);

      return {
        template,
        metrics: {
          campaigns: templateCampaigns.length,
          leads: templateLeads.length,
          cpl: templateLeads.length > 0 ? totalSpend / templateLeads.length : 0,
          conversionRate: templateLeads.length > 0 ? (conversions / templateLeads.length) * 100 : 0,
          roi: totalSpend > 0 ? (revenue - totalSpend) / totalSpend * 100 : 0
        }
      };
    });

    return templatePerformance
      .sort((a, b) => b.metrics.roi - a.metrics.roi)
      .slice(0, limit);
  }
}

// Export singleton instance
export const analyticsService = new AnalyticsService();
