/**
 * Comprehensive Facebook Data Service for PressureMax
 * Handles fetching and processing all Facebook campaign data with proper filtering
 */

import { facebookIntegration } from './facebookIntegration';

export interface FacebookCampaignMetrics {
  impressions: number;
  clicks: number;
  spend: number;
  ctr: number;
  cpc: number;
  cpl: number;
  leads: number;
  reach: number;
  frequency: number;
  cost_per_1000_people_reached: number;
}

export interface FacebookAdCreative {
  id: string;
  name: string;
  title: string;
  body: string;
  call_to_action_type: string;
  image_url?: string;
  video_id?: string;
  thumbnail_url?: string;
  link_url?: string;
  object_story_spec?: any;
}

export interface FacebookAd {
  id: string;
  name: string;
  status: string;
  creative: FacebookAdCreative;
  metrics: FacebookCampaignMetrics;
  created_time: string;
  updated_time: string;
}

export interface FacebookAdSet {
  id: string;
  name: string;
  status: string;
  daily_budget?: number;
  lifetime_budget?: number;
  bid_strategy: string;
  bid_amount?: number;
  optimization_goal: string;
  billing_event: string;
  targeting: any;
  metrics: FacebookCampaignMetrics;
  ads: FacebookAd[];
  created_time: string;
  updated_time: string;
}

export interface FacebookCampaignData {
  id: string;
  name: string;
  objective: string;
  status: string;
  daily_budget?: number;
  lifetime_budget?: number;
  special_ad_categories: string[];
  metrics: FacebookCampaignMetrics;
  adSets: FacebookAdSet[];
  created_time: string;
  updated_time: string;
  account_id: string;
  account_name: string;
}

export interface FacebookLead {
  id: string;
  created_time: string;
  ad_id: string;
  adset_id: string;
  campaign_id: string;
  form_id: string;
  field_data: Array<{
    name: string;
    values: string[];
  }>;
  is_organic: boolean;
  platform: string;
}

class FacebookDataService {
  private readonly API_VERSION = 'v23.0';
  private readonly BASE_URL = 'https://graph.facebook.com';
  private lastRequestTime = 0;
  private readonly MIN_REQUEST_INTERVAL = 500; // 500ms between requests

  /**
   * Rate limiting helper
   */
  private async waitForRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    
    if (timeSinceLastRequest < this.MIN_REQUEST_INTERVAL) {
      const waitTime = this.MIN_REQUEST_INTERVAL - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    
    this.lastRequestTime = Date.now();
  }

  /**
   * Make Facebook Graph API call with rate limiting
   */
  private async makeGraphAPICall(endpoint: string, params: any = {}): Promise<any> {
    await this.waitForRateLimit();
    
    const facebookStatus = facebookIntegration.getIntegrationStatus();
    
    if (!facebookStatus.isConnected || !facebookStatus.accessToken) {
      throw new Error('Facebook account not connected');
    }

    const url = new URL(`${this.BASE_URL}/${this.API_VERSION}/${endpoint}`);
    url.searchParams.append('access_token', facebookStatus.accessToken);
    
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null) {
        url.searchParams.append(key, params[key].toString());
      }
    });

    const response = await fetch(url.toString());
    const result = await response.json();
    
    if (result.error) {
      if (result.error.code === 4 || result.error.message.includes('rate limit')) {
        console.warn('⚠️ Facebook API rate limit reached');
        throw new Error('Rate limit reached');
      }
      throw new Error(`Facebook API Error: ${result.error.message}`);
    }
    
    return result;
  }

  /**
   * Get connected ad accounts (filtered by user's connected pages)
   */
  async getConnectedAdAccounts(): Promise<any[]> {
    const facebookStatus = facebookIntegration.getIntegrationStatus();
    
    if (!facebookStatus.isConnected) {
      throw new Error('Facebook not connected');
    }

    // Get all ad accounts
    const allAdAccounts = await this.makeGraphAPICall('/me/adaccounts', {
      fields: 'id,account_id,name,account_status,currency,timezone_name,amount_spent,balance'
    });

    // Filter ad accounts based on connected pages
    const connectedPageIds = facebookStatus.pages.map(page => page.id);
    const filteredAccounts = [];

    for (const account of allAdAccounts.data || []) {
      try {
        // Check if this ad account is associated with any of our connected pages
        const accountPages = await this.makeGraphAPICall(`${account.id}/pages`, {
          fields: 'id,name'
        });

        const hasConnectedPage = accountPages.data?.some((page: any) => 
          connectedPageIds.includes(page.id)
        );

        if (hasConnectedPage || connectedPageIds.length === 0) {
          filteredAccounts.push(account);
        }
      } catch (error) {
        console.warn(`Could not check pages for account ${account.name}:`, error);
        // Include account if we can't check (better to include than exclude)
        filteredAccounts.push(account);
      }
    }

    return filteredAccounts;
  }

  /**
   * Get campaign metrics with insights
   */
  private async getCampaignMetrics(campaignId: string): Promise<FacebookCampaignMetrics> {
    try {
      const insights = await this.makeGraphAPICall(`${campaignId}/insights`, {
        fields: 'impressions,clicks,spend,ctr,cpc,reach,frequency,cost_per_1000_people_reached,actions',
        time_range: JSON.stringify({
          since: '2024-01-01',
          until: new Date().toISOString().split('T')[0]
        })
      });

      const data = insights.data?.[0] || {};
      
      // Extract lead generation actions
      const actions = data.actions || [];
      const leadAction = actions.find((action: any) => action.action_type === 'lead');
      const leads = leadAction ? parseInt(leadAction.value) : 0;
      
      return {
        impressions: parseInt(data.impressions) || 0,
        clicks: parseInt(data.clicks) || 0,
        spend: parseFloat(data.spend) || 0,
        ctr: parseFloat(data.ctr) || 0,
        cpc: parseFloat(data.cpc) || 0,
        cpl: leads > 0 ? (parseFloat(data.spend) || 0) / leads : 0,
        leads: leads,
        reach: parseInt(data.reach) || 0,
        frequency: parseFloat(data.frequency) || 0,
        cost_per_1000_people_reached: parseFloat(data.cost_per_1000_people_reached) || 0
      };
    } catch (error) {
      console.warn(`Could not fetch metrics for campaign ${campaignId}:`, error);
      return {
        impressions: 0,
        clicks: 0,
        spend: 0,
        ctr: 0,
        cpc: 0,
        cpl: 0,
        leads: 0,
        reach: 0,
        frequency: 0,
        cost_per_1000_people_reached: 0
      };
    }
  }

  /**
   * Get ad creative details
   */
  private async getAdCreative(creativeId: string): Promise<FacebookAdCreative> {
    try {
      const creative = await this.makeGraphAPICall(creativeId, {
        fields: 'id,name,title,body,call_to_action_type,image_url,video_id,thumbnail_url,object_story_spec'
      });

      return {
        id: creative.id,
        name: creative.name || '',
        title: creative.title || creative.object_story_spec?.page_post_spec?.message || '',
        body: creative.body || creative.object_story_spec?.page_post_spec?.description || '',
        call_to_action_type: creative.call_to_action_type || 'LEARN_MORE',
        image_url: creative.image_url,
        video_id: creative.video_id,
        thumbnail_url: creative.thumbnail_url,
        link_url: creative.object_story_spec?.link_data?.link || '',
        object_story_spec: creative.object_story_spec
      };
    } catch (error) {
      console.warn(`Could not fetch creative ${creativeId}:`, error);
      return {
        id: creativeId,
        name: 'Unknown Creative',
        title: '',
        body: '',
        call_to_action_type: 'LEARN_MORE'
      };
    }
  }

  /**
   * Get leads from Facebook Lead Ads
   */
  async getLeadsFromCampaign(campaignId: string): Promise<FacebookLead[]> {
    try {
      const leads = await this.makeGraphAPICall(`${campaignId}/leads`, {
        fields: 'id,created_time,ad_id,adset_id,campaign_id,form_id,field_data,is_organic,platform'
      });

      return leads.data || [];
    } catch (error) {
      console.warn(`Could not fetch leads for campaign ${campaignId}:`, error);
      return [];
    }
  }

  /**
   * Get all leads from all connected campaigns
   */
  async getAllLeads(): Promise<FacebookLead[]> {
    try {
      const campaigns = await this.getAllCampaignData();
      const allLeads: FacebookLead[] = [];

      for (const campaign of campaigns) {
        // Only get leads from lead generation campaigns
        if (campaign.objective === 'LEAD_GENERATION') {
          try {
            const campaignLeads = await this.getLeadsFromCampaign(campaign.id);
            allLeads.push(...campaignLeads);
          } catch (error) {
            console.warn(`Error fetching leads for campaign ${campaign.name}:`, error);
          }
        }
      }

      return allLeads;
    } catch (error) {
      console.error('Error fetching Facebook leads:', error);
      return [];
    }
  }

  /**
   * Get comprehensive campaign data for connected accounts only
   */
  async getAllCampaignData(): Promise<FacebookCampaignData[]> {
    try {
      const connectedAccounts = await this.getConnectedAdAccounts();
      const allCampaigns: FacebookCampaignData[] = [];

      console.log(`📊 Fetching campaigns from ${connectedAccounts.length} connected ad accounts`);

      for (const account of connectedAccounts) {
        try {
          console.log(`🔍 Processing account: ${account.name}`);
          
          // Get campaigns for this account
          const campaigns = await this.makeGraphAPICall(`${account.id}/campaigns`, {
            fields: 'id,name,objective,status,created_time,updated_time,daily_budget,lifetime_budget,special_ad_categories',
            limit: 50
          });

          for (const campaign of campaigns.data || []) {
            try {
              // Get campaign metrics
              const metrics = await this.getCampaignMetrics(campaign.id);

              // Get ad sets for this campaign
              const adSets = await this.makeGraphAPICall(`${campaign.id}/adsets`, {
                fields: 'id,name,status,daily_budget,lifetime_budget,billing_event,optimization_goal,bid_strategy,bid_amount,targeting,created_time,updated_time',
                limit: 25
              });

              const processedAdSets: FacebookAdSet[] = [];

              for (const adSet of adSets.data || []) {
                try {
                  // Get ad set metrics
                  const adSetMetrics = await this.getCampaignMetrics(adSet.id);

                  // Get ads for this ad set
                  const ads = await this.makeGraphAPICall(`${adSet.id}/ads`, {
                    fields: 'id,name,status,creative,created_time,updated_time',
                    limit: 25
                  });

                  const processedAds: FacebookAd[] = [];

                  for (const ad of ads.data || []) {
                    try {
                      const adMetrics = await this.getCampaignMetrics(ad.id);
                      let creative: FacebookAdCreative;

                      if (ad.creative?.id) {
                        creative = await this.getAdCreative(ad.creative.id);
                      } else {
                        creative = {
                          id: 'unknown',
                          name: 'Unknown Creative',
                          title: '',
                          body: '',
                          call_to_action_type: 'LEARN_MORE'
                        };
                      }

                      processedAds.push({
                        id: ad.id,
                        name: ad.name,
                        status: ad.status,
                        creative,
                        metrics: adMetrics,
                        created_time: ad.created_time,
                        updated_time: ad.updated_time
                      });
                    } catch (error) {
                      console.warn(`Error processing ad ${ad.id}:`, error);
                    }
                  }

                  processedAdSets.push({
                    id: adSet.id,
                    name: adSet.name,
                    status: adSet.status,
                    daily_budget: adSet.daily_budget,
                    lifetime_budget: adSet.lifetime_budget,
                    bid_strategy: adSet.bid_strategy,
                    bid_amount: adSet.bid_amount,
                    optimization_goal: adSet.optimization_goal,
                    billing_event: adSet.billing_event,
                    targeting: adSet.targeting,
                    metrics: adSetMetrics,
                    ads: processedAds,
                    created_time: adSet.created_time,
                    updated_time: adSet.updated_time
                  });
                } catch (error) {
                  console.warn(`Error processing ad set ${adSet.id}:`, error);
                }
              }

              allCampaigns.push({
                id: campaign.id,
                name: campaign.name,
                objective: campaign.objective,
                status: campaign.status,
                daily_budget: campaign.daily_budget,
                lifetime_budget: campaign.lifetime_budget,
                special_ad_categories: campaign.special_ad_categories || [],
                metrics,
                adSets: processedAdSets,
                created_time: campaign.created_time,
                updated_time: campaign.updated_time,
                account_id: account.account_id,
                account_name: account.name
              });

              console.log(`✅ Processed campaign: ${campaign.name}`);
            } catch (error) {
              console.warn(`Error processing campaign ${campaign.name}:`, error);
            }
          }
        } catch (error) {
          console.error(`Error processing account ${account.name}:`, error);
        }
      }

      console.log(`🎉 Successfully processed ${allCampaigns.length} campaigns`);
      return allCampaigns;
    } catch (error) {
      console.error('Error fetching Facebook campaign data:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const facebookDataService = new FacebookDataService();
