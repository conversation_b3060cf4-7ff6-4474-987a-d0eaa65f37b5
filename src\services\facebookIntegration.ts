/**
 * Facebook Business Integration Service for PressureMax
 * Handles OAuth flow, account management, and campaign launching
 */

export interface FacebookUser {
  id: string;
  name: string;
  email: string;
}

export interface FacebookPage {
  id: string;
  name: string;
  access_token: string;
  category: string;
  tasks: string[];
}

export interface FacebookAdAccount {
  id: string;
  account_id: string;
  name: string;
  account_status: number;
  currency: string;
  timezone_name: string;
  amount_spent?: string;
  balance?: string;
}

export interface FacebookIntegrationStatus {
  isConnected: boolean;
  user?: FacebookUser;
  pages: FacebookPage[];
  adAccounts: FacebookAdAccount[];
  accessToken?: string;
  expiresAt?: Date;
  permissions: string[];
}

export class FacebookIntegrationService {
  private readonly APP_ID = '***************';
  private readonly REDIRECT_URI = `${window.location.origin}/integrations/facebook/callback`;
  private readonly REQUIRED_PERMISSIONS = [
    'ads_management',
    'ads_read',
    'business_management',
    'pages_read_engagement',
    'pages_manage_ads',
    'pages_show_list'
  ];

  private status: FacebookIntegrationStatus = {
    isConnected: false,
    pages: [],
    adAccounts: [],
    permissions: []
  };

  constructor() {
    // Load saved integration status on initialization
    this.loadSavedIntegration();

    // Initialize Facebook SDK early for better session handling
    this.initializeFacebookSDK().catch(console.error);
  }

  /**
   * Initialize Facebook SDK
   */
  async initializeFacebookSDK(): Promise<void> {
    return new Promise((resolve) => {
      // Check if SDK is already loaded
      if (window.FB) {
        resolve();
        return;
      }

      // Load Facebook SDK
      window.fbAsyncInit = () => {
        window.FB.init({
          appId: this.APP_ID,
          cookie: true, // Enable cookie support for session persistence
          xfbml: true,
          version: 'v23.0',
          status: true, // Check login status on init
          frictionlessRequests: true // Enable frictionless requests
        });

        // Check for existing session after SDK initialization
        this.checkExistingSession().catch(console.error);

        resolve();
      };

      // Load SDK script
      const script = document.createElement('script');
      script.async = true;
      script.defer = true;
      script.crossOrigin = 'anonymous';
      script.src = 'https://connect.facebook.net/en_US/sdk.js';
      document.head.appendChild(script);
    });
  }

  /**
   * Check for existing Facebook session on SDK initialization
   */
  private async checkExistingSession(): Promise<void> {
    try {
      if (!window.FB) return;

      window.FB.getLoginStatus((response: any) => {
        if (response.status === 'connected' && response.authResponse) {
          console.log('🔄 Found existing Facebook session, restoring...');
          this.handleAuthResponse(response.authResponse)
            .then(() => {
              console.log('✅ Facebook session restored successfully');
            })
            .catch((error) => {
              console.error('❌ Error restoring Facebook session:', error);
              // Clear invalid session data
              this.clearSavedIntegration();
            });
        } else if (response.status === 'not_authorized') {
          console.log('⚠️ Facebook session exists but app not authorized');
          this.clearSavedIntegration();
        } else {
          console.log('ℹ️ No existing Facebook session found');
          // Check if we have saved data that might be stale
          if (this.status.isConnected && this.status.accessToken) {
            console.log('🧹 Clearing stale Facebook integration data');
            this.clearSavedIntegration();
          }
        }
      });
    } catch (error) {
      console.error('Error checking existing Facebook session:', error);
    }
  }

  /**
   * Check if HTTPS is required and available
   */
  private isHttpsRequired(): boolean {
    return window.location.protocol !== 'https:' && window.location.hostname !== 'localhost';
  }

  /**
   * Start Facebook OAuth flow
   */
  async connectFacebookAccount(): Promise<void> {
    // Check for HTTPS requirement
    if (window.location.protocol !== 'https:' && window.location.hostname !== 'localhost') {
      throw new Error('Facebook login requires HTTPS. Please access the application via HTTPS.');
    }

    await this.initializeFacebookSDK();

    return new Promise((resolve, reject) => {
      try {
        window.FB.login((response) => {
          if (response.authResponse) {
            this.handleAuthResponse(response.authResponse)
              .then(() => resolve())
              .catch(reject);
          } else {
            reject(new Error('Facebook login was cancelled or failed'));
          }
        }, {
          scope: this.REQUIRED_PERMISSIONS.join(','),
          return_scopes: true,
          auth_type: 'rerequest'
        });
      } catch (error) {
        if (error.message && error.message.includes('http pages')) {
          reject(new Error('Facebook login requires HTTPS. Please access the application via HTTPS.'));
        } else {
          reject(error);
        }
      }
    });
  }

  /**
   * Handle Facebook auth response
   */
  private async handleAuthResponse(authResponse: any): Promise<void> {
    try {
      const { accessToken, userID, expiresIn } = authResponse;
      
      // Get user info
      const user = await this.makeGraphAPICall('/me', { fields: 'id,name,email' }, accessToken);
      
      // Get user's pages
      const pagesResponse = await this.makeGraphAPICall('/me/accounts', {
        fields: 'id,name,access_token,category,tasks'
      }, accessToken);
      
      // Get user's ad accounts
      const adAccountsResponse = await this.makeGraphAPICall('/me/adaccounts', {
        fields: 'id,account_id,name,account_status,currency,timezone_name,amount_spent,balance'
      }, accessToken);

      // Get granted permissions
      const permissionsResponse = await this.makeGraphAPICall('/me/permissions', {}, accessToken);
      const grantedPermissions = permissionsResponse.data
        .filter((p: any) => p.status === 'granted')
        .map((p: any) => p.permission);

      // Update status
      this.status = {
        isConnected: true,
        user: user,
        pages: pagesResponse.data || [],
        adAccounts: adAccountsResponse.data || [],
        accessToken: accessToken,
        expiresAt: new Date(Date.now() + (expiresIn * 1000)),
        permissions: grantedPermissions
      };

      // Store in localStorage for persistence
      this.saveStatus();

      console.log('✅ Facebook integration connected successfully');
    } catch (error) {
      console.error('❌ Error handling Facebook auth response:', error);
      throw error;
    }
  }

  /**
   * Make Facebook Graph API call
   */
  private async makeGraphAPICall(endpoint: string, params: any = {}, accessToken?: string): Promise<any> {
    const token = accessToken || this.status.accessToken;
    if (!token) {
      throw new Error('No access token available');
    }

    const url = new URL(`https://graph.facebook.com/v23.0${endpoint}`);
    url.searchParams.append('access_token', token);
    
    Object.keys(params).forEach(key => {
      url.searchParams.append(key, params[key]);
    });

    const response = await fetch(url.toString());
    const data = await response.json();

    if (data.error) {
      throw new Error(`Facebook API Error: ${data.error.message}`);
    }

    return data;
  }

  /**
   * Load saved integration status
   */
  loadSavedIntegration(): FacebookIntegrationStatus {
    try {
      const saved = localStorage.getItem('facebook_integration');
      if (saved) {
        const parsed = JSON.parse(saved);
        if (parsed.expiresAt) {
          parsed.expiresAt = new Date(parsed.expiresAt);

          // Check if token is expired (with 5 minute buffer)
          const bufferTime = 5 * 60 * 1000; // 5 minutes in milliseconds
          if (parsed.expiresAt.getTime() - bufferTime < Date.now()) {
            console.log('🔄 Facebook token expired or expiring soon, clearing saved data');
            this.clearSavedIntegration();
            return this.status;
          }
        }

        // Validate required fields
        if (parsed.isConnected && (!parsed.accessToken || !parsed.user)) {
          console.log('⚠️ Invalid saved Facebook data, clearing...');
          this.clearSavedIntegration();
          return this.status;
        }

        this.status = parsed;
        console.log('✅ Loaded saved Facebook integration status');
      }
    } catch (error) {
      console.error('Error loading saved Facebook integration:', error);
      this.clearSavedIntegration();
    }

    return this.status;
  }

  /**
   * Disconnect Facebook account
   */
  disconnectFacebookAccount(): void {
    this.status = {
      isConnected: false,
      pages: [],
      adAccounts: [],
      permissions: []
    };
    localStorage.removeItem('facebook_integration');
    console.log('Facebook integration disconnected');
  }

  /**
   * Get current integration status
   */
  getIntegrationStatus(): FacebookIntegrationStatus {
    return { ...this.status };
  }

  /**
   * Save current status to localStorage
   */
  private saveStatus(): void {
    try {
      localStorage.setItem('facebook_integration', JSON.stringify({
        ...this.status,
        expiresAt: this.status.expiresAt?.toISOString()
      }));
      console.log('💾 Facebook integration status saved to localStorage');
    } catch (error) {
      console.error('Error saving Facebook integration status:', error);
    }
  }

  /**
   * Clear saved integration data
   */
  private clearSavedIntegration(): void {
    try {
      localStorage.removeItem('facebook_integration');
      this.status = {
        isConnected: false,
        pages: [],
        adAccounts: [],
        permissions: []
      };
      console.log('🧹 Facebook integration data cleared');
    } catch (error) {
      console.error('Error clearing Facebook integration data:', error);
    }
  }

  /**
   * Check if user has required permissions
   */
  hasRequiredPermissions(): boolean {
    return this.REQUIRED_PERMISSIONS.every(permission => 
      this.status.permissions.includes(permission)
    );
  }

  /**
   * Get missing permissions
   */
  getMissingPermissions(): string[] {
    return this.REQUIRED_PERMISSIONS.filter(permission => 
      !this.status.permissions.includes(permission)
    );
  }

  /**
   * Test connection by making a simple API call
   */
  async testConnection(): Promise<boolean> {
    if (!this.status.isConnected || !this.status.accessToken) {
      return false;
    }

    try {
      await this.makeGraphAPICall('/me');
      return true;
    } catch (error) {
      console.error('Facebook connection test failed:', error);
      return false;
    }
  }

  /**
   * Refresh access token
   */
  async refreshAccessToken(): Promise<void> {
    // Check for HTTPS requirement
    if (window.location.protocol !== 'https:' && window.location.hostname !== 'localhost') {
      throw new Error('Facebook login requires HTTPS. Please access the application via HTTPS.');
    }

    await this.initializeFacebookSDK();

    return new Promise((resolve, reject) => {
      try {
        window.FB.getLoginStatus((response) => {
          if (response.status === 'connected') {
            this.handleAuthResponse(response.authResponse)
              .then(() => resolve())
              .catch(reject);
          } else {
            reject(new Error('Facebook session expired'));
          }
        });
      } catch (error) {
        if (error.message && error.message.includes('http pages')) {
          reject(new Error('Facebook login requires HTTPS. Please access the application via HTTPS.'));
        } else {
          reject(error);
        }
      }
    });
  }

  /**
   * Get the default/primary Facebook page
   */
  getDefaultPage(): FacebookPage | null {
    if (this.status.pages.length === 0) return null;

    // Try to find a page with ads management permissions
    const pageWithAdsPermission = this.status.pages.find(page =>
      page.tasks && page.tasks.includes('ADVERTISE')
    );

    if (pageWithAdsPermission) return pageWithAdsPermission;

    // Otherwise return the first page
    return this.status.pages[0];
  }

  /**
   * Get pages that can be used for advertising
   */
  getAdvertisingPages(): FacebookPage[] {
    return this.status.pages.filter(page =>
      page.tasks && page.tasks.includes('ADVERTISE')
    );
  }

  /**
   * Get ad accounts for the connected user
   */
  async getAdAccounts(): Promise<FacebookAdAccount[]> {
    if (!this.status.isConnected || !this.status.accessToken) {
      throw new Error('Facebook account not connected');
    }

    try {
      const response = await this.makeGraphAPICall('/me/adaccounts', {
        fields: 'id,account_id,name,account_status,currency,timezone_name,amount_spent,balance'
      });

      const adAccounts: FacebookAdAccount[] = response.data.map((account: any) => ({
        id: account.id,
        account_id: account.account_id,
        name: account.name,
        account_status: account.account_status,
        currency: account.currency,
        timezone_name: account.timezone_name,
        amount_spent: account.amount_spent,
        balance: account.balance
      }));

      // Update status with ad accounts
      this.status.adAccounts = adAccounts;
      this.saveStatus();

      return adAccounts;
    } catch (error) {
      console.error('Error fetching ad accounts:', error);
      throw error;
    }
  }
}

// Extend window interface for Facebook SDK
declare global {
  interface Window {
    FB: any;
    fbAsyncInit: () => void;
  }
}

// Export singleton instance
export const facebookIntegration = new FacebookIntegrationService();
