/**
 * Supabase Authentication Service for PressureMax
 * Handles user authentication using Supabase Auth
 */

import { supabase, handleSupabaseError } from '../lib/supabase';
import { User } from '../types/database';

export interface AuthResponse {
  user: User;
  session: any;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface SignupData {
  email: string;
  password: string;
  name: string;
  company_name: string;
  phone?: string;
  timezone?: string;
}

export interface PasswordResetRequest {
  email: string;
}

class SupabaseAuthService {
  /**
   * Sign up a new user
   */
  async signup(data: SignupData): Promise<AuthResponse> {
    try {
      // Create auth user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: data.email,
        password: data.password,
      });

      if (authError) {
        throw new Error(handleSupabaseError(authError));
      }

      if (!authData.user) {
        throw new Error('Failed to create user account');
      }

      // Create user profile
      const profileData = {
        id: authData.user.id,
        email: data.email,
        name: data.name,
        company_name: data.company_name,
        phone: data.phone || null,
        role: 'user' as const,
        permissions: [],
        plan: 'starter' as const,
        subscription_status: 'active' as const,
        timezone: data.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone,
        business_hours: {
          start: '09:00',
          end: '17:00',
          days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .insert(profileData)
        .select()
        .single();

      if (profileError) {
        // Clean up auth user if profile creation fails
        await supabase.auth.admin.deleteUser(authData.user.id);
        throw new Error(handleSupabaseError(profileError));
      }

      return {
        user: this.mapProfileToUser(profile),
        session: authData.session
      };
    } catch (error) {
      console.error('Signup error:', error);
      throw error;
    }
  }

  /**
   * Log in an existing user
   */
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });

      if (error) {
        throw new Error(handleSupabaseError(error));
      }

      if (!data.user) {
        throw new Error('Login failed');
      }

      // Get user profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', data.user.id)
        .single();

      if (profileError) {
        throw new Error(handleSupabaseError(profileError));
      }

      // Update last login
      await supabase
        .from('profiles')
        .update({ 
          last_login_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', data.user.id);

      return {
        user: this.mapProfileToUser(profile),
        session: data.session
      };
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  /**
   * Log out the current user
   */
  async logout(): Promise<void> {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        throw new Error(handleSupabaseError(error));
      }
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(data: PasswordResetRequest): Promise<void> {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(data.email, {
        redirectTo: `http://localhost:5173/reset-password`,
      });

      if (error) {
        throw new Error(handleSupabaseError(error));
      }
    } catch (error) {
      console.error('Password reset request error:', error);
      throw error;
    }
  }

  /**
   * Reset password with new password
   */
  async resetPassword(newPassword: string): Promise<void> {
    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      });

      if (error) {
        throw new Error(handleSupabaseError(error));
      }
    } catch (error) {
      console.error('Password reset error:', error);
      throw error;
    }
  }

  /**
   * Get current user profile
   */
  async getCurrentUser(): Promise<User | null> {
    // Wrap the entire method in a timeout
    return Promise.race([
      this._getCurrentUserInternal(),
      new Promise<User | null>((resolve) =>
        setTimeout(() => {
          console.log('getCurrentUser: Method timeout, returning null');
          resolve(null);
        }, 5000)
      )
    ]);
  }

  private async _getCurrentUserInternal(): Promise<User | null> {
    try {
      console.log('getCurrentUser: Starting...');
      const { data: { user }, error } = await supabase.auth.getUser();

      if (error) {
        console.log('getCurrentUser: Auth error:', error.message);
        // If it's just a session missing error, return null (not logged in)
        if (error.message?.includes('Auth session missing')) {
          return null;
        }
        throw new Error(handleSupabaseError(error));
      }

      if (!user) {
        console.log('getCurrentUser: No user in session');
        return null;
      }

      console.log('getCurrentUser: Found user in session:', user.email, 'ID:', user.id);

      // Get user profile with timeout
      console.log('getCurrentUser: Querying profile for user ID:', user.id);

      const profilePromise = supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      // Add timeout to the profile query
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Profile query timeout')), 3000)
      );

      const { data: profile, error: profileError } = await Promise.race([
        profilePromise,
        timeoutPromise
      ]) as any;

      if (profileError) {
        console.log('getCurrentUser: Profile error:', profileError.message, 'Code:', profileError.code);
        // If profile doesn't exist or there's an error, create a minimal user from auth session
        console.log('getCurrentUser: Creating minimal user from auth session');
        return {
          id: user.id,
          email: user.email || '',
          name: user.user_metadata?.name || 'User',
          company_name: user.user_metadata?.company_name || 'Company',
          phone: user.user_metadata?.phone || null,
          role: 'user' as const,
          permissions: [],
          plan: 'starter' as const,
          subscription_status: 'active' as const,
          facebook_access_token: null,
          facebook_ad_account_id: null,
          facebook_page_id: null,
          vapi_api_key: null,
          vapi_assistant_id: null,
          timezone: 'UTC',
          business_hours: {
            start: '09:00',
            end: '17:00',
            days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
          },
          created_at: new Date(),
          updated_at: new Date(),
          last_login_at: undefined
        };
      }

      console.log('getCurrentUser: Found profile:', profile.email);
      return this.mapProfileToUser(profile);
    } catch (error) {
      // Don't log session missing errors as they're normal
      if (!error.message?.includes('Auth session missing')) {
        console.error('Get current user error:', error);
      }
      return null;
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(updates: Partial<User>): Promise<User> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) {
        throw new Error('Not authenticated');
      }

      const { data: profile, error } = await supabase
        .from('profiles')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id)
        .select()
        .single();

      if (error) {
        throw new Error(handleSupabaseError(error));
      }

      return this.mapProfileToUser(profile);
    } catch (error) {
      console.error('Profile update error:', error);
      throw error;
    }
  }

  /**
   * Check if user is authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      return !!user;
    } catch {
      return false;
    }
  }

  /**
   * Get current session
   */
  async getSession() {
    const { data: { session } } = await supabase.auth.getSession();
    return session;
  }

  /**
   * Listen to auth state changes
   */
  onAuthStateChange(callback: (event: string, session: any) => void) {
    return supabase.auth.onAuthStateChange(callback);
  }

  /**
   * Map Supabase profile to User type
   */
  private mapProfileToUser(profile: any): User {
    return {
      id: profile.id,
      email: profile.email,
      name: profile.name,
      company_name: profile.company_name,
      phone: profile.phone,
      role: profile.role,
      permissions: profile.permissions || [],
      plan: profile.plan,
      subscription_status: profile.subscription_status,
      facebook_access_token: profile.facebook_access_token,
      facebook_ad_account_id: profile.facebook_ad_account_id,
      facebook_page_id: profile.facebook_page_id,
      vapi_api_key: profile.vapi_api_key,
      vapi_assistant_id: profile.vapi_assistant_id,
      timezone: profile.timezone,
      business_hours: profile.business_hours,
      created_at: new Date(profile.created_at),
      updated_at: new Date(profile.updated_at),
      last_login_at: profile.last_login_at ? new Date(profile.last_login_at) : undefined,
    };
  }
}

// Export singleton instance
export const supabaseAuthService = new SupabaseAuthService();
